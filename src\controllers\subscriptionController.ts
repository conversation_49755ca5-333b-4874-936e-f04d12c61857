import { Request, Response } from "express";
import { poolPromise } from "../config/db";
import sql from "mssql";
import Strip<PERSON> from "stripe";
import axios from 'axios';
import qs from 'qs';
import { getDbSecretsFromKeyVault } from "../config/keyvault";

interface Secrets {
  AzureContainerName: string;
  AzureStorageAccountKey: string;
  AzureStorageAccountName: string;
  ClientName: string;
  DbName: string;
  DbPassword: string;
  DbServer: string;
  DbUser: string;
  StripeSecretKey: string;
  StripeWebhookSecret: string;
  FrontendUrl: string;
  SpClientId: string;
  SpClientSecret: string;
  SpTenantId: string;
  SpTokenScope: string;
}

/**
 * @swagger
 * /api/v1/create-subscription:
 *   post:
 *     summary: Create a subscription for a user
 *     description: Registers a new user and creates a subscription (trial, free, or paid). For trial and free users, completes registration and returns user details. For paid users, initiates a Stripe checkout session. Fails if the email already exists, 2 Way account association fails, or other critical steps encounter errors.
 *     tags: [Subscription]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 description: User's email address (required).
 *                 example: <EMAIL>
 *               priceId:
 *                 type: string
 *                 description: Stripe price ID for paid subscriptions. Required for paid users, optional for trial/free (use "free" for free plan).
 *                 example: price_123456789
 *               trialPeriodDays:
 *                 type: integer
 *                 description: Number of trial days for trial subscriptions. Optional, defaults to 30 for trial users.
 *                 example: 30
 *               fullName:
 *                 type: string
 *                 description: User's full name.
 *                 example: John Doe
 *               userRole:
 *                 type: string
 *                 description: User's role within the company.
 *                 example: Admin
 *               company:
 *                 type: string
 *                 description: Company name (optional).
 *                 example: Acme Corp
 *               industry:
 *                 type: string
 *                 description: Industry of the company (optional).
 *                 example: Technology
 *               phone:
 *                 type: string
 *                 description: User's phone number (optional).
 *                 example: +1234567890
 *               extension:
 *                 type: string
 *                 description: Phone extension (optional).
 *                 example: 123
 *               address:
 *                 type: string
 *                 description: Street address (optional, used to construct businessAddress).
 *                 example: 123 Main St
 *               city:
 *                 type: string
 *                 description: City (optional, used to construct businessAddress).
 *                 example: New York
 *               country:
 *                 type: string
 *                 description: Country (optional, used to construct businessAddress).
 *                 example: USA
 *               zipCode:
 *                 type: string
 *                 description: ZIP or postal code (optional, used to construct businessAddress).
 *                 example: 10001
 *               accountName:
 *                 type: string
 *                 description: Account name (optional).
 *                 example: Acme Account
 *               pkg:
 *                 type: string
 *                 description: Package type (e.g., "free" for free plan, optional).
 *                 example: free
 *               duration:
 *                 type: string
 *                 description: Subscription duration (optional).
 *                 example: monthly
 *               purchase:
 *                 type: string
 *                 description: Type of purchase ("trial" for trial users, optional).
 *                 example: trial
 *     responses:
 *       201:
 *         description: User created successfully (for trial and free users).
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - type: object
 *                   description: Response for trial users.
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: Trial user registered successfully.
 *                     loginEmail:
 *                       type: string
 *                       description: Email used for login from ClientUsers table.
 *                       example: <EMAIL>
 *                     userPlan:
 *                       type: string
 *                       description: User's subscription plan.
 *                       example: 30 Days Trial
 *                     redirectUrl:
 *                       type: string
 *                       description: URL to redirect to after successful registration.
 *                       example: https://frontend.example.com/success?userType=trial
 *                 - type: object
 *                   description: Response for free users.
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: Free user registered successfully.
 *                     loginEmail:
 *                       type: string
 *                       description: Email used for login from ClientUsers table.
 *                       example: <EMAIL>
 *                     userPlan:
 *                       type: string
 *                       description: User's subscription plan.
 *                       example: Free Plan
 *                     redirectUrl:
 *                       type: string
 *                       description: URL to redirect to after successful registration.
 *                       example: https://frontend.example.com/success?userType=free
 *       200:
 *         description: Stripe checkout session created successfully (for paid users).
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 sessionId:
 *                   type: string
 *                   description: Stripe checkout session ID to redirect to for payment.
 *                   example: cs_123456789
 *       400:
 *         description: Bad request due to missing required parameters or invalid input.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message describing the issue.
 *                   example: Email is required.
 *                   enum:
 *                     - Email is required.
 *                     - Valid priceId is required for paid subscriptions.
 *       409:
 *         description: Conflict - user with this email already exists.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message describing the issue.
 *                   example: User with this email already exists.
 *                 message:
 *                   type: string
 *                   description: Additional guidance for the user.
 *                   example: Please log in or use a different email to sign up.
 *       500:
 *         description: Internal server error due to database, Stripe, or other unexpected issues.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message describing the issue.
 *                   example: Unable to retrieve user details.
 *       503:
 *         description: Service unavailable due to failure in associating a 2 Way account or executing related stored procedures.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message describing the issue.
 *                   example: Unable to sign up now! Please try again later.
 */
export const createSubscription = async (req: any, res: any) => {
  // Await secrets
  const secrets: Secrets = await getDbSecretsFromKeyVault();
  const stripe = new Stripe(secrets.StripeSecretKey);

  const {
    email,
    priceId,
    trialPeriodDays,
    fullName,
    userRole,
    company,
    industry,
    phone,
    extension,
    address,
    city,
    country,
    accountName,
    zipCode,
    pkg,
    duration,
    purchase,
  } = req.body;

  const businessAddress = `${address}, ${city}, ${country}${zipCode ? ` - ${zipCode}` : ""}`;

  const pool = await poolPromise;
  let transaction;

  try {
    if (!email) {
      return res.status(400).json({ error: "Email is required." });
    }

    // Check if user already exists
    let result = await pool.request()
      .input("email", sql.NVarChar, email)
      .query("SELECT * FROM Clients WHERE email = @email");

    let user = result.recordset[0];
    if (user) {
      return res.status(409).json({
        error: "User with this email already exists.",
        message: "Please log in or use a different email to sign up."
      });
    }

    // Start transaction
    transaction = new sql.Transaction(pool);
    await transaction.begin();

    // === 2 Way Account Association ===
    let associatedAccount;
    try {
      const request = transaction.request();
      result = await request.execute('sp_Dep_Associate_Account');
      associatedAccount = result.recordset[0]?.AssociatedAccount;
      if (!associatedAccount) {
        console.error('2 Way Account was not associated.');
        throw new Error("Unable to sign up now! Please try again later");
      }

      // Load entities
      try {
        const request = transaction.request();
        request.input('Client', sql.NVarChar(50), associatedAccount);
        request.input('Industry', sql.NVarChar(255), industry);
        await request.execute('sp_Dep_LoadEntities');
      } catch (err) {
        console.error('Error loading entities:', err);
        throw new Error("Unable to load entities.");
      }

      // Load tokens
      try {
        const request = transaction.request();
        request.input('Client', sql.NVarChar(50), associatedAccount);
        request.input('pkg', sql.NVarChar(255), pkg);
        await request.execute('sp_Dep_LoadTokens');
      } catch (err) {
        console.error('Error loading tokens:', err);
        throw new Error("Unable to load tokens.");
      }
    } catch (err) {
      console.error('Error executing stored procedure:', err);
      return res.status(503).json({error: "Unable to sign up now! Please try again later"});
    }

    // === TRIAL USERS ===
    if (purchase === "trial") {
      await transaction.request()
        .input("email", sql.NVarChar, email)
        .input("fullName", sql.NVarChar, fullName)
        .input("userRole", sql.NVarChar, userRole)
        .input("company", sql.NVarChar, company || null)
        .input("industry", sql.NVarChar, industry || null)
        .input("phone", sql.NVarChar, phone || null)
        .input("extension", sql.NVarChar, extension || null)
        .input("businessAddress", sql.NVarChar, businessAddress)
        .input("accountName", sql.NVarChar, accountName || null)
        .input("pkg", sql.NVarChar, pkg || null)
        .input("duration", sql.NVarChar, duration || null)
        .input("purchase", sql.NVarChar, purchase || null)
        .input("country", sql.NVarChar, country || null)
        .input("city", sql.NVarChar, city || null)
        .input("zipCode", sql.NVarChar, zipCode || null)
        .input("createdAt", sql.DateTime, new Date())
        .input("updatedAt", sql.DateTime, new Date())
        .input("stripeSubscriptionId", sql.NVarChar, null)
        .input("stripeCustomerId", sql.NVarChar, null)
        .input("stripeStatus", sql.NVarChar, "TRIAL")
        .input("userPlan", sql.NVarChar, `${trialPeriodDays || 30} Days Trial`)
        .input("2Way_account", sql.NVarChar, associatedAccount)
        .query(`
          INSERT INTO Clients 
          (email, fullName, userRole, company, industry, phone, extension, businessAddress, accountName, pkg, duration, purchase, country, city, zipCode, createdAt, updatedAt, stripeSubscriptionId, stripeCustomerId, stripeStatus, userPlan, [2Way_account])
          VALUES 
          (@email, @fullName, @userRole, @company, @industry, @phone, @extension, @businessAddress, @accountName, @pkg, @duration, @purchase, @country, @city, @zipCode, @createdAt, @updatedAt, @stripeSubscriptionId, @stripeCustomerId, @stripeStatus, @userPlan, @2Way_account)
        `);

      // Send Microsoft Invite
      try {
        await sendUserInvitation({
          invitedUserEmailAddress: email,
          invitedUserDisplayName: fullName,
          inviteRedirectUrl: secrets.FrontendUrl,
          customizedMessageBody: 'Welcome to 2 Way Analytics! Please follow the instructions in this email to finalize your signup.',
          companyName: company,
          sendInvitationMessage: false,
        });
      } catch (inviteErr) {
        console.error('Invitation failed:', inviteErr);
      }

      // Fetch login email and user plan
      let loginEmail;
      let userPlan;
      try {
        const request = transaction.request();
        request.input('clientEmail', sql.NVarChar, email);
        const query = `
          SELECT
              cu.Email,
              c.userPlan
          FROM [dbo].[Clients] c
          INNER JOIN [dbo].[ClientUsers] cu ON c.[2Way_account] = cu.Client
          WHERE c.email = @clientEmail
        `;
        result = await request.query(query);
        loginEmail = result.recordset[0]?.Email;
        userPlan = result.recordset[0]?.userPlan;
        if (!loginEmail || !userPlan) {
          throw new Error("Failed to retrieve login email or user plan.");
        }
      } catch (error) {
        console.error('Database query failed:', error);
        return res.status(500).json({ error: "Unable to retrieve user details." });
      }

      await transaction.commit();
      return res.status(201).json({
        message: "Trial user registered successfully.",
        loginEmail,
        userPlan,
        redirectUrl: `${secrets.FrontendUrl}/success?userType=trial`,
      });
    }

    // === FREE USERS ===
    if (pkg === "free" || priceId === "free") {
      await transaction.request()
        .input("email", sql.NVarChar, email)
        .input("fullName", sql.NVarChar, fullName)
        .input("userRole", sql.NVarChar, userRole)
        .input("company", sql.NVarChar, company || null)
        .input("industry", sql.NVarChar, industry || null)
        .input("phone", sql.NVarChar, phone || null)
        .input("extension", sql.NVarChar, extension || null)
        .input("businessAddress", sql.NVarChar, businessAddress)
        .input("accountName", sql.NVarChar, accountName || null)
        .input("pkg", sql.NVarChar, pkg || null)
        .input("duration", sql.NVarChar, duration || null)
        .input("purchase", sql.NVarChar, purchase || null)
        .input("country", sql.NVarChar, country || null)
        .input("city", sql.NVarChar, city || null)
        .input("zipCode", sql.NVarChar, zipCode || null)
        .input("createdAt", sql.DateTime, new Date())
        .input("updatedAt", sql.DateTime, new Date())
        .input("stripeSubscriptionId", sql.NVarChar, null)
        .input("stripeCustomerId", sql.NVarChar, null)
        .input("stripeStatus", sql.NVarChar, "FREE")
        .input("userPlan", sql.NVarChar, "Free Plan")
        .input("2Way_account", sql.NVarChar, associatedAccount)
        .query(`
          INSERT INTO Clients 
          (email, fullName, userRole, company, industry, phone, extension, businessAddress, accountName, pkg, duration, purchase, country, city, zipCode, createdAt, updatedAt, stripeSubscriptionId, stripeCustomerId, stripeStatus, userPlan, [2Way_account])
          VALUES 
          (@email, @fullName, @userRole, @company, @industry, @phone, @extension, @businessAddress, @accountName, @pkg, @duration, @purchase, @country, @city, @zipCode, @createdAt, @updatedAt, @stripeSubscriptionId, @stripeCustomerId, @stripeStatus, @userPlan, @2Way_account)
        `);

      // Send Microsoft Invite
      try {
        await sendUserInvitation({
          invitedUserEmailAddress: email,
          invitedUserDisplayName: fullName,
          inviteRedirectUrl: secrets.FrontendUrl,
          customizedMessageBody: 'Welcome to 2 Way Analytics! Please follow the instructions in this email to finalize your signup.',
          companyName: company,
          sendInvitationMessage: false,
        });
      } catch (inviteErr) {
        console.error('Invitation failed:', inviteErr);
      }

      // Fetch login email and user plan
      let loginEmail;
      let userPlan;
      try {
        const request = transaction.request();
        request.input('clientEmail', sql.NVarChar, email);
        const query = `
          SELECT
              cu.Email,
              c.userPlan
          FROM [dbo].[Clients] c
          INNER JOIN [dbo].[ClientUsers] cu ON c.[2Way_account] = cu.Client
          WHERE c.email = @clientEmail
        `;
        result = await request.query(query);
        loginEmail = result.recordset[0]?.Email;
        userPlan = result.recordset[0]?.userPlan;
        if (!loginEmail || !userPlan) {
          throw new Error("Failed to retrieve login email or user plan.");
        }
      } catch (error) {
        console.error('Database query failed:', error);
        return res.status(500).json({ error: "Unable to retrieve user details." });
      }

      await transaction.commit();
      return res.status(201).json({
        message: "Free user registered successfully.",
        userPlan: "Free Plan",
        redirectUrl: `${secrets.FrontendUrl}/success?userType=free`,
        loginEmail,
      });
    }

    // === PAID USERS ===
    if (!priceId || priceId === "free") {
      return res.status(400).json({ error: "Valid priceId is required for paid subscriptions." });
    }

    // Insert new user
    await transaction.request()
      .input("email", sql.NVarChar, email)
      .input("fullName", sql.NVarChar, fullName)
      .input("userRole", sql.NVarChar, userRole)
      .input("company", sql.NVarChar, company || null)
      .input("industry", sql.NVarChar, industry || null)
      .input("phone", sql.NVarChar, phone || null)
      .input("extension", sql.NVarChar, extension || null)
      .input("businessAddress", sql.NVarChar, businessAddress)
      .input("accountName", sql.NVarChar, accountName || null)
      .input("pkg", sql.NVarChar, pkg || null)
      .input("duration", sql.NVarChar, duration || null)
      .input("purchase", sql.NVarChar, purchase || null)
      .input("country", sql.NVarChar, country || null)
      .input("city", sql.NVarChar, city || null)
      .input("zipCode", sql.NVarChar, zipCode || null)
      .input("createdAt", sql.DateTime, new Date())
      .input("updatedAt", sql.DateTime, new Date())
      .input("2Way_account", sql.NVarChar, associatedAccount)
      .query(`
        INSERT INTO Clients 
        (email, fullName, userRole, company, industry, phone, extension, businessAddress, accountName, pkg, duration, purchase, country, city, zipCode, createdAt, updatedAt, [2Way_account])
        VALUES 
        (@email, @fullName, @userRole, @company, @industry, @phone, @extension, @businessAddress, @accountName, @pkg, @duration, @purchase, @country, @city, @zipCode, @createdAt, @updatedAt, @2Way_account)
      `);

    // Create Stripe Customer and Checkout Session
    const customer = await stripe.customers.create({ email });
    const stripeCustomerId = customer.id;

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      mode: "subscription",
      customer: stripeCustomerId,
      line_items: [{ price: priceId, quantity: 1 }],
      subscription_data: {
        trial_period_days: trialPeriodDays,
      },
      success_url: `${secrets.FrontendUrl}/success?userType=paid&customerId=${stripeCustomerId}`,
      cancel_url: `${secrets.FrontendUrl}/cancel`,
    });

    // Update stripeCustomerId
    await transaction.request()
      .input("stripeCustomerId", sql.NVarChar, stripeCustomerId)
      .input("email", sql.NVarChar, email)
      .input("updatedAt", sql.DateTime, new Date())
      .query(`
        UPDATE Clients
        SET stripeCustomerId = @stripeCustomerId,
            updatedAt = @updatedAt
        WHERE email = @email
      `);

    // Send Microsoft Invite
    try {
      await sendUserInvitation({
        invitedUserEmailAddress: email,
        invitedUserDisplayName: fullName,
        inviteRedirectUrl: secrets.FrontendUrl,
        customizedMessageBody: 'Welcome to 2 Way Analytics! Please follow the instructions in this email to finalize your signup.',
        companyName: company,
        sendInvitationMessage: false,
      });
    } catch (inviteErr) {
      console.error('Invitation failed:', inviteErr);
    }

    await transaction.commit();
    res.status(200).json({ sessionId: session.id });

  } catch (err: any) {
    if (transaction) {
      try {
        await transaction.rollback();
      } catch (rollbackErr) {
        console.error('Rollback failed:', rollbackErr);
      }
    }
    console.error("Subscription Error:", err);
    res.status(500).json({ error: err.message });
  }
};

/**
 * @swagger
 * /api/v1/get-user-by-stripe-id:
 *   post:
 *     summary: Get user details by Stripe Customer ID
 *     tags: [Subscription]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               stripeCustomerId:
 *                 type: string
 *     responses:
 *       200:
 *         description: User details retrieved successfully.
 *       400:
 *         description: Missing required parameters.
 *       404:
 *         description: User not found.
 */
export const getUserByStripeCustomerId = async (req: any, res: any) => {
  const { stripeCustomerId } = req.body;

  if (!stripeCustomerId) {
    return res.status(400).json({ error: "stripeCustomerId is required." });
  }

  try {
    const pool = await poolPromise;

    const result = await pool.request()
      .input("stripeCustomerId", sql.NVarChar, stripeCustomerId)
      .query("SELECT * FROM Clients WHERE stripeCustomerId = @stripeCustomerId");

    const user = result.recordset[0];

    if (!user) {
      return res.status(404).json({ error: "User not found." });
    }

    return res.json({ user: user.email, userPlan: user.userPlan, stripeStatus: user.stripeStatus });
  } catch (err: any) {
    console.error("Error fetching user by stripeCustomerId:", err);
    res.status(500).json({ error: err.message });
  }
};

/**
 * @swagger
 * /api/v1/webhook:
 *   post:
 *     summary: Handle Stripe webhook events
 *     description: This endpoint handles Stripe webhook events to update user subscriptions.
 *     tags: [Subscription]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               stripe-signature:
 *                 type: string
 *     responses:
 *       200:
 *         description: Webhook event processed successfully.
 *       400:
 *         description: Invalid webhook event.
 */
export const handleWebhook = async (req: Request, res: Response): Promise<void> => {
  const secrets: Secrets = await getDbSecretsFromKeyVault();
  const stripe = new Stripe(secrets.StripeSecretKey);

  const sig = req.headers["stripe-signature"] as string;
  let loginEmail;
  let userPlan;
  try {
    let userInfo;
    const event = stripe.webhooks.constructEvent(
      req.body,
      sig,
      secrets.StripeWebhookSecret
    );

    if (event.type === "checkout.session.completed") {
      const session = event.data.object as Stripe.Checkout.Session;
      const email = session.customer_details?.email;
      if (email) {
        const lineItems = await stripe.checkout.sessions.listLineItems(session.id);

        if (lineItems.data.length > 0) {
          const planId = lineItems.data[0].price?.id;

          const pool = await poolPromise;
          const result = await pool
            .request()
            .input("stripeSubscriptionId", sql.NVarChar, session.subscription as string)
            .input("stripeStatus", sql.NVarChar, "active")
            .input("userPlan", sql.NVarChar, planId)
            .input("email", sql.NVarChar, email)
            .query(`
              UPDATE Clients
              SET stripeSubscriptionId = @stripeSubscriptionId, stripeStatus = @stripeStatus, userPlan = @userPlan
              WHERE email = @email
            `);
          if (result.rowsAffected[0] === 0) {
            console.warn(`No rows updated for email: ${email}. Check if email exists in Clients table.`);
          }
        } else {
          console.warn('No line items found in session.');
        }
        try {
          const pool = await poolPromise;
          const request = pool.request();

          // Add input parameter for the email filter
          request.input('clientEmail', sql.VarChar, email);

          const query = `
            SELECT
                cu.Email,
                c.userPlan
            FROM [dbo].[Clients] c
            INNER JOIN [dbo].[ClientUsers] cu ON c.[2Way_account] = cu.Client
            WHERE c.email = @clientEmail
        `;

          const result = await request.query(query);
          loginEmail = result.recordset[0].Email;
          userPlan = result.recordset[0].userPlan;
        } catch (error) {
          console.error('Database query failed:', error);
          throw error;
        }
      }
      res.json({ received: true, loginEmail: loginEmail, userPlan: userPlan });
    } else {
      console.log(`Unhandled event type: ${event.type}`);
      res.status(400).send(`Unhandled event type: ${event.type}`);
    }
  } catch (err) {
    const errorMessage = (err as Error).message;
    console.error('Webhook error:', errorMessage);
    res.status(400).send(`Webhook Error: ${errorMessage}`);
  }
};

export async function getAzureAccessToken(): Promise<string> {
  const secrets: Secrets = await getDbSecretsFromKeyVault();
  const tenantId = secrets.SpTenantId;
  const clientId = secrets.SpClientId;
  const clientSecret = secrets.SpClientSecret;
  const scope = secrets.SpTokenScope;

  const tokenUrl = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`;

  const data = qs.stringify({
    grant_type: 'client_credentials',
    client_id: clientId,
    client_secret: clientSecret,
    scope: scope,
  });

  try {
    const response = await axios.post(tokenUrl, data, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    return response.data.access_token;
  } catch (error: any) {
    console.error('Error fetching Azure access token:', error.response?.data || error.message);
    throw new Error('Failed to get Azure access token');
  }
}

interface InvitationPayload {
  invitedUserEmailAddress: string;
  invitedUserDisplayName: string;
  inviteRedirectUrl: string;
  customizedMessageBody?: string;
  companyName?: string;
  sendInvitationMessage?: boolean;
}

export async function sendUserInvitation(payload: InvitationPayload): Promise<any> {
  const accessToken = await getAzureAccessToken();

  const url = 'https://graph.microsoft.com/v1.0/invitations';

  const body = {
    invitedUserEmailAddress: payload.invitedUserEmailAddress,
    invitedUserDisplayName: payload.invitedUserDisplayName,
    inviteRedirectUrl: payload.inviteRedirectUrl,
    sendUserInvitation: false,
    customized(dt: any): string | undefined {
      return dt.customizedMessageBody ?? 'Hello, This is a message from the Microsoft Graph API!';
    },
    companyName: payload.companyName ?? 'Default Company',
  };

  try {
    const response = await axios.post(url, body, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    return response.data;
  } catch (error: any) {
    console.error('Error sending invitation:', error.response?.data || error.message);
    throw new Error('Failed to send user invitation');
  }
}